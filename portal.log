[32m[2025-07-21 11:00:57.688] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: { roleInfos: [ [Object], [Object], [Object] ] },
  signature: '6639c2f943a344f6b75819692996da66'
}
[32m[2025-07-21 11:00:57.691] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: [
    { id: 1, groupName: '用户管理组', description: '管理用户相关权限' },
    { id: 2, groupName: '系统管理组', description: '管理系统相关权限' },
    { id: 3, groupName: '数据管理组', description: '管理数据相关权限' }
  ],
  signature: '4821618c8d67cd2e736baa5863b9aeeb'
}
[32m[2025-07-21 11:01:11.690] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[31m[2025-07-21 11:01:11.915] [ERROR] console - [39m(node:5674) [DEP0066] DeprecationWarning: OutgoingMessage.prototype._headers is deprecated
(Use `node --trace-deprecation ...` to show where the warning was created)
[0mGET /sysuser [36m304 [0m229.392 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m1.504 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.703 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m2.976 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m2.876 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.886 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m0.885 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m0.915 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.121 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m2.093 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m2.259 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m3.396 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m3.468 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m3.845 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m4.052 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.789 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.931 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m10.026 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m2.791 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m2.844 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m3.034 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m3.238 ms - -[0m
[0mGET /module/constants.js [36m304 [0m3.377 ms - -[0m
[0mGET /module/base.js [36m304 [0m0.663 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m0.660 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m0.661 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.632 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.664 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m0.634 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m0.677 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.852 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.970 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m2.103 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m0.659 ms - -[0m
[32m[2025-07-21 11:01:12.929] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /images/logoding.gif [36m304 [0m5.676 ms - -[0m
[32m[2025-07-21 11:01:12.942] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:01:12.943] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m37.858 ms - -[0m
[0mGET /favicon.ico [36m304 [0m0.725 ms - -[0m
[32m[2025-07-21 11:01:15.035] [INFO] console - [39m系统拦截器，seesion中的用户信息:undefined
[32m[2025-07-21 11:01:15.036] [INFO] console - [39m跳转到登录页面
[0mGET /login [32m200 [0m22.484 ms - 421[0m
[32m[2025-07-21 11:01:16.277] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /log [36m304 [0m178.564 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.536 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.869 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m0.961 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.947 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.007 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m0.925 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m0.961 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m0.985 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.068 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m0.962 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.021 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m0.972 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.032 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m0.694 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m0.527 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m2.689 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m2.841 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m2.985 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m3.119 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m0.621 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.755 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.776 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.886 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.616 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.740 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m1.844 ms - -[0m
[0mGET /module/systemmgr/log.js [36m304 [0m0.570 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.094 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.350 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m0.540 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.513 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m0.568 ms - -[0m
[0mGET /images/hovers.png [36m304 [0m1.159 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.222 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m0.620 ms - -[0m
[32m[2025-07-21 11:01:16.629] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:16.641] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: {
    count: 3,
    records: [ [Object], [Object], [Object] ],
    pageNo: 1,
    pageSize: 10,
    totalPages: 1
  },
  signature: '9d9ad87ef2c97c9502c1655d81c005fd'
}
[0mPOST /log/q [31m500 [0m55.841 ms - 903[0m
[32m[2025-07-21 11:01:25.428] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m130.273 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.540 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.062 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.137 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.612 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.676 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.819 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.900 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.189 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.278 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m0.558 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m0.546 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.130 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.223 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.190 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.311 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.204 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.254 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.126 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.230 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m0.530 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m0.558 ms - -[0m
[0mGET /module/constants.js [36m304 [0m0.601 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.691 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.808 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.917 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.651 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.666 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m0.620 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.328 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.393 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.610 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.697 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.744 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m0.609 ms - -[0m
[32m[2025-07-21 11:01:25.828] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:25.837] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:01:25.839] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m12.541 ms - -[0m
[32m[2025-07-21 11:01:28.064] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:28.072] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: 1,
  pageSize: '50',
  totalPages: 1
}
[0mGET /department [36m304 [0m107.573 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.541 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.459 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m0.586 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.557 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m0.531 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.159 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.371 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m0.605 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m0.607 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.131 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.253 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.234 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.324 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m0.570 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.203 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.329 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m0.568 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.205 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.314 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m0.541 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.728 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.812 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.915 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.139 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.247 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.605 ms - -[0m
[0mGET /module/systemmgr/department.js [36m304 [0m0.612 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.784 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.886 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.980 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.723 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.825 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.931 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m0.625 ms - -[0m
[32m[2025-07-21 11:01:28.390] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:28.397] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[0mPOST /department/q [32m200 [0m12.899 ms - 142[0m
[32m[2025-07-21 11:01:29.329] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /log [36m304 [0m111.699 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.776 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.035 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.085 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.121 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.258 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.120 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.285 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m0.863 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m0.554 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m0.587 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m0.569 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m0.608 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.157 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.243 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.270 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.278 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.214 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.275 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.810 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.937 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m2.004 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.134 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.228 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m0.592 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m0.585 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.576 ms - -[0m
[0mGET /module/systemmgr/log.js [36m304 [0m0.600 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m0.564 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m0.563 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.167 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.320 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.789 ms - -[0m
[0mGET /images/hovers.png [36m304 [0m1.853 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.835 ms - -[0m
[32m[2025-07-21 11:01:29.762] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /images/logoding.gif [36m304 [0m4.111 ms - -[0m
[32m[2025-07-21 11:01:29.770] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: {
    count: 3,
    records: [ [Object], [Object], [Object] ],
    pageNo: 1,
    pageSize: 10,
    totalPages: 1
  },
  signature: '9d9ad87ef2c97c9502c1655d81c005fd'
}
[0mPOST /log/q [31m500 [0m42.364 ms - 903[0m
[32m[2025-07-21 11:01:50.265] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m108.667 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.597 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.379 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.331 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.741 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.253 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.336 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.302 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.362 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m0.601 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m0.629 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.956 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.964 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m2.064 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.890 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.931 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m2.045 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.997 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m2.002 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m2.019 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.929 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.981 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.945 ms - -[0m
[0mGET /module/base.js [36m304 [0m0.616 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m0.608 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m0.588 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.619 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.662 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.899 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.916 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.983 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.589 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m0.594 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m0.608 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m1.027 ms - -[0m
[32m[2025-07-21 11:01:50.670] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:50.678] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:01:50.679] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m10.468 ms - -[0m
[32m[2025-07-21 11:01:53.067] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m98.608 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m1.596 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.559 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.537 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.175 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.206 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.260 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m0.849 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m0.894 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m0.466 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m0.493 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.869 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.917 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m2.011 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m2.075 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m0.924 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.063 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.005 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.032 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m0.884 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m0.929 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m0.891 ms - -[0m
[0mGET /module/constants.js [36m304 [0m0.937 ms - -[0m
[0mGET /module/base.js [36m304 [0m0.842 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m0.884 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m0.874 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.923 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.475 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.357 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.385 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.444 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.593 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.559 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.642 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m0.522 ms - -[0m
[32m[2025-07-21 11:01:53.310] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:01:53.316] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:01:53.317] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m9.134 ms - -[0m
[0mGET /favicon.ico [36m304 [0m0.617 ms - -[0m
[32m[2025-07-21 11:02:00.201] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m95.358 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.531 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m1.392 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.339 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.199 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m2.061 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m2.093 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.079 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m2.081 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.037 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.075 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m0.987 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.021 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.034 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m2.078 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.568 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m2.661 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.663 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.601 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.440 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.618 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m2.240 ms - -[0m
[0mGET /module/constants.js [36m304 [0m2.064 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.528 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m2.572 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.460 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m1.485 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.570 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.397 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.462 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.508 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.049 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.203 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m0.538 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m1.712 ms - -[0m
[32m[2025-07-21 11:02:00.367] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:00.373] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:02:00.374] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m9.247 ms - -[0m
[0mGET /favicon.ico [36m304 [0m0.549 ms - -[0m
[32m[2025-07-21 11:02:02.261] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:02.267] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: 1,
  pageSize: '50',
  totalPages: 1
}
[0mGET /department [36m304 [0m89.318 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.604 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.513 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.026 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.203 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.761 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.864 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.896 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m2.265 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m2.278 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m2.366 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m2.413 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.152 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.168 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.709 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.732 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.746 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.612 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.646 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.717 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.576 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.644 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.638 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.519 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.524 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.599 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.549 ms - -[0m
[0mGET /module/systemmgr/department.js [36m304 [0m0.519 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m2.139 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.724 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.839 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.598 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.625 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.693 ms - -[0m
[32m[2025-07-21 11:02:02.431] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /images/logoding.gif [36m304 [0m2.318 ms - -[0m
[32m[2025-07-21 11:02:02.436] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[0mPOST /department/q [32m200 [0m11.696 ms - 142[0m
[32m[2025-07-21 11:02:07.543] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:07.549] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: { roleInfos: [ [Object], [Object], [Object] ] },
  signature: '6639c2f943a344f6b75819692996da66'
}
[0mGET /role [36m304 [0m96.787 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.727 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.499 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.601 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.610 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.616 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.076 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.252 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m0.863 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.139 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.159 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.966 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.132 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.572 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.556 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.133 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.938 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.109 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.518 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.457 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.037 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.073 ms - -[0m
[0mGET /module/constants.js [36m304 [0m0.650 ms - -[0m
[0mGET /module/base.js [36m304 [0m0.689 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m0.639 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m0.687 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.377 ms - -[0m
[0mGET /module/systemmgr/role.js [36m304 [0m0.381 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.103 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.091 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.133 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.122 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.163 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.205 ms - -[0m
[32m[2025-07-21 11:02:09.445] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m89.108 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.577 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m0.931 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.946 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m2.064 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m2.048 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m2.101 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.087 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m0.966 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.006 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.463 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.577 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m2.637 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.579 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m2.718 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.523 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.560 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m0.980 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m3.038 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m2.518 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m2.539 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m2.603 ms - -[0m
[0mGET /module/constants.js [36m304 [0m2.584 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.430 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.489 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.512 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.449 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.507 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.510 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.513 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.548 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.521 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m0.527 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m0.549 ms - -[0m
[32m[2025-07-21 11:02:09.621] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /images/logoding.gif [36m304 [0m3.363 ms - -[0m
[32m[2025-07-21 11:02:09.630] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:02:09.631] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m12.366 ms - -[0m
[0mGET /images/home-ionc-tc-hv.png [36m304 [0m0.627 ms - -[0m
[0mGET /login/lo [36m304 [0m26.844 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.min.css [36m304 [0m0.582 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.min.css [36m304 [0m2.331 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m2.324 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m2.413 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m2.425 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.098 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.283 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m0.530 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.756 ms - -[0m
[0mGET /module/login/login.js [36m304 [0m1.741 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.789 ms - -[0m
[0mGET /images/lobg.png [36m304 [0m1.570 ms - -[0m
[0mGET /images/del.png [36m304 [0m1.209 ms - -[0m
[32m[2025-07-21 11:02:20.167] [INFO] login - [39m检测到演示账户，直接使用离线演示模式
[32m[2025-07-21 11:02:20.168] [INFO] login - [39m离线演示模式登录成功: admin
[32m[2025-07-21 11:02:20.168] [INFO] login - [39mSession中的用户信息: {"userId":"demo-user-001","accountName":"admin","userName":"演示管理员","email":"<EMAIL>","status":1,"deptId":"bb590fd43d554e1390750686bbcd5aae"}
[0mPOST /login/li [32m200 [0m3.600 ms - -[0m
[32m[2025-07-21 11:02:20.189] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:20.195] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: [
    { id: 1, orderSn: 'ORD001', status: '已完成', amount: 100 },
    { id: 2, orderSn: 'ORD002', status: '待处理', amount: 200 }
  ],
  signature: '6e66238911c7f3d9392a3da988b2a472'
}
[0mGET /index [32m200 [0m77.147 ms - 7641[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.560 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.798 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m0.626 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.570 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.660 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.799 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.876 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m2.168 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m2.246 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m2.360 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m3.581 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.758 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.774 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.160 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.203 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.132 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.244 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.050 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.128 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.061 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.276 ms - -[0m
[0mGET /module/constants.js [36m304 [0m1.508 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.470 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m2.608 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m1.560 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m1.609 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.444 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.472 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.544 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.514 ms - -[0m
[0mGET /images/welcome_img.jpg [36m304 [0m0.896 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.002 ms - -[0m
[32m[2025-07-21 11:02:25.319] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:25.325] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: 1,
  pageSize: '50',
  totalPages: 1
}
[0mGET /department [36m304 [0m77.561 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.698 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.477 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m0.926 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m0.965 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.430 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.474 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.917 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.907 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.961 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m8.026 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.767 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.813 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m2.119 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m2.531 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m2.300 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.975 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m2.115 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.927 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m0.600 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m2.781 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m2.353 ms - -[0m
[0mGET /module/constants.js [36m304 [0m2.340 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.996 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.995 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m2.773 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m1.107 ms - -[0m
[0mGET /module/systemmgr/department.js [36m304 [0m0.495 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m2.999 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m3.080 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m3.113 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m3.145 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m3.212 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m3.301 ms - -[0m
[32m[2025-07-21 11:02:25.490] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /images/logoding.gif [36m304 [0m0.500 ms - -[0m
[32m[2025-07-21 11:02:25.495] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      deptId: 1,
      name: '业务创新中心',
      description: '负责业务创新和产品研发',
      status: 1,
      createTime: '2018-01-15 10:30:00',
      updateTime: '2024-01-15 10:30:00'
    },
    {
      deptId: 2,
      name: '新媒体事业部',
      description: '负责新媒体业务运营',
      status: 1,
      createTime: '2018-02-20 14:15:00',
      updateTime: '2024-02-20 14:15:00'
    },
    {
      deptId: 3,
      name: '服务交付中心',
      description: '负责客户服务交付',
      status: 1,
      createTime: '2018-03-10 09:45:00',
      updateTime: '2024-03-10 09:45:00'
    },
    {
      deptId: 4,
      name: '企业云计算',
      description: '负责企业云计算业务',
      status: 1,
      createTime: '2018-04-05 16:20:00',
      updateTime: '2024-04-05 16:20:00'
    },
    {
      deptId: 5,
      name: '技术研发部',
      description: '负责技术研发和创新',
      status: 1,
      createTime: '2018-05-12 11:30:00',
      updateTime: '2024-05-12 11:30:00'
    },
    {
      deptId: 6,
      name: '市场营销部',
      description: '负责市场营销和品牌推广',
      status: 1,
      createTime: '2018-06-18 13:45:00',
      updateTime: '2024-06-18 13:45:00'
    },
    {
      deptId: 7,
      name: '人力资源部',
      description: '负责人力资源管理',
      status: 1,
      createTime: '2018-07-25 15:10:00',
      updateTime: '2024-07-25 15:10:00'
    },
    {
      deptId: 8,
      name: '财务部',
      description: '负责财务管理',
      status: 1,
      createTime: '2018-08-30 10:25:00',
      updateTime: '2024-08-30 10:25:00'
    }
  ],
  total: 8,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[0mPOST /department/q [32m200 [0m11.457 ms - 142[0m
[32m[2025-07-21 11:02:26.810] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[0mGET /sysuser [36m304 [0m88.770 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.693 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m1.077 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.163 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m2.019 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m2.077 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m2.139 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m3.451 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.753 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m2.997 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.749 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.717 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.553 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.571 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m3.333 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m2.206 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m2.195 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m2.310 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.057 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.052 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.527 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.613 ms - -[0m
[0mGET /module/constants.js [36m304 [0m3.127 ms - -[0m
[0mGET /module/base.js [36m304 [0m2.002 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.988 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m2.005 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.465 ms - -[0m
[0mGET /module/sysuser/sysuser.js [36m304 [0m0.542 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.613 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.749 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.811 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m0.566 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.113 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.159 ms - -[0m
[0mGET /images/logoding.gif [36m304 [0m1.856 ms - -[0m
[32m[2025-07-21 11:02:26.981] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:26.986] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  records: [
    {
      userId: 1,
      accountName: 'admin',
      userName: '系统管理员',
      email: '<EMAIL>',
      phone: '***********',
      department: '技术部',
      status: 1,
      roleId: 1,
      roleName: '管理员',
      createTime: '2018-01-15 10:30:00'
    },
    {
      userId: 2,
      accountName: 'user1',
      userName: '张三',
      email: '<EMAIL>',
      phone: '***********',
      department: '业务部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-02-20 14:15:00'
    },
    {
      userId: 3,
      accountName: 'user2',
      userName: '李四',
      email: '<EMAIL>',
      phone: '***********',
      department: '市场部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-03-10 09:45:00'
    },
    {
      userId: 4,
      accountName: 'user3',
      userName: '王五',
      email: '<EMAIL>',
      phone: '***********',
      department: '财务部',
      status: 0,
      roleId: 3,
      roleName: '财务',
      createTime: '2018-04-05 16:20:00'
    },
    {
      userId: 5,
      accountName: 'user4',
      userName: '赵六',
      email: '<EMAIL>',
      phone: '***********',
      department: '人事部',
      status: 1,
      roleId: 2,
      roleName: '普通用户',
      createTime: '2018-05-12 11:30:00'
    }
  ],
  total: 5,
  pageNo: '1',
  pageSize: '50',
  totalPages: 1
}
[31m[2025-07-21 11:02:26.987] [ERROR] sysuser - [39m{}
[0mPOST /sysuser/q [32m200 [0m8.288 ms - -[0m
[32m[2025-07-21 11:02:28.437] [INFO] console - [39m系统拦截器，seesion中的用户信息:[object Object]
[32m[2025-07-21 11:02:28.442] [INFO] console - [39mRestApiProxy响应: null {
  status: 'SUCCESS',
  data: { roleInfos: [ [Object], [Object], [Object] ] },
  signature: '6639c2f943a344f6b75819692996da66'
}
[0mGET /role [36m304 [0m90.674 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap.css [36m304 [0m0.560 ms - -[0m
[0mGET /plugins/bootstrap/css/bootstrap-theme.css [36m304 [0m0.562 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/css/bootstrap-datetimepicker-2.1.9.css [36m304 [0m1.180 ms - -[0m
[0mGET /plugins/jquery-confirm2/css/jquery-confirm.css [36m304 [0m1.231 ms - -[0m
[0mGET /plugins/validator/jquery.validator.css [36m304 [0m1.539 ms - -[0m
[0mGET /plugins/jquery.selectlist/css/jquery.selectlist.css [36m304 [0m1.630 ms - -[0m
[0mGET /stylesheets/common.css [36m304 [0m1.726 ms - -[0m
[0mGET /stylesheets/shopm.css [36m304 [0m1.690 ms - -[0m
[0mGET /stylesheets/template2/stylesheets/uec-main.css [36m304 [0m1.770 ms - -[0m
[0mGET /plugins/jquery/jquery-1.11.3.js [36m304 [0m1.768 ms - -[0m
[0mGET /plugins/jquery/jquery.cookie.js [36m304 [0m1.568 ms - -[0m
[0mGET /plugins/jquery/jquery.form.js [36m304 [0m1.601 ms - -[0m
[0mGET /plugins/bootstrap/js/bootstrap.js [36m304 [0m1.723 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/moment.js [36m304 [0m1.627 ms - -[0m
[0mGET /plugins/bootstrap-datetimepicker/js/bootstrap-datetimepicker-2.1.9.js [36m304 [0m1.667 ms - -[0m
[0mGET /plugins/bootstrap-paginator/js/bootstrap-paginator.js [36m304 [0m1.712 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/pretty.js [36m304 [0m1.529 ms - -[0m
[0mGET /plugins/jquery-confirm2/js/jquery-confirm.js [36m304 [0m1.546 ms - -[0m
[0mGET /plugins/validator/jquery.validator.js [36m304 [0m1.602 ms - -[0m
[0mGET /plugins/validator/zh-CN.js [36m304 [0m1.477 ms - -[0m
[0mGET /plugins/jquery.selectlist/js/jquery.selectlist.js [36m304 [0m1.534 ms - -[0m
[0mGET /module/constants.js [36m304 [0m3.050 ms - -[0m
[0mGET /module/base.js [36m304 [0m1.931 ms - -[0m
[0mGET /module/common/common.js [36m304 [0m1.969 ms - -[0m
[0mGET /images/logo.svg [36m304 [0m2.041 ms - -[0m
[0mGET /module/header/header.js [36m304 [0m0.489 ms - -[0m
[0mGET /module/systemmgr/role.js [36m304 [0m0.529 ms - -[0m
[0mGET /images/home_hd_bg.jpg [36m304 [0m1.546 ms - -[0m
[0mGET /images/urse-hv.png [36m304 [0m1.581 ms - -[0m
[0mGET /images/home-ionc-tc.png [36m304 [0m1.623 ms - -[0m
[0mGET /images/nav-top.jpg [36m304 [0m1.622 ms - -[0m
[0mGET /images/tan_babg.png [36m304 [0m1.718 ms - -[0m
[0mGET /fonts/iconfont.woff [36m304 [0m1.751 ms - -[0m
